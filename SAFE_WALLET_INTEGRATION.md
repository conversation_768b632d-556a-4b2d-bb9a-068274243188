# Safe Wallet Integration Documentation

This document describes the implementation of Safe Wallet transaction services in the vito-interface project, integrating with the vito-contracts repository for on-chain functionality.

## Overview

The Safe Wallet integration provides:
- Real wallet connection via MetaMask
- Transaction proposal to SafeTxPool contract
- Multi-signature transaction signing
- Transaction execution through Safe contracts
- Real-time transaction status monitoring

## Architecture

### Core Components

1. **BlockchainService** (`client/src/services/blockchainService.ts`)
   - Manages blockchain connections and Safe SDK integration
   - Handles SafeTxPool contract interactions
   - Provides transaction lifecycle management

2. **WalletService** (`client/src/services/walletService.ts`)
   - Manages wallet connections (MetaMask, WalletConnect)
   - Handles network switching and account management
   - Provides signing capabilities

3. **SafeWallet Models** (`client/src/models/SafeWallet.ts`)
   - Enhanced transaction types with Safe-specific fields
   - Real implementations replacing mock functions
   - Integration with blockchain services

4. **UI Components**
   - **SafeConnection**: Wallet connection and Safe info display
   - **TransactionManager**: Transaction signing and execution interface
   - Enhanced command system for Safe operations

### Smart Contract Integration

The integration connects to the SafeTxPool contract from vito-contracts:

- **Transaction Proposal**: Submit transactions to the pool
- **Signature Collection**: Collect multi-sig signatures
- **Execution**: Execute transactions when threshold is met
- **Status Tracking**: Monitor transaction lifecycle

## Setup Instructions

### 1. Install Dependencies

```bash
cd client
npm install
```

New dependencies added:
- `@safe-global/api-kit`: Safe API integration
- `@safe-global/protocol-kit`: Safe SDK core
- `@safe-global/relay-kit`: Transaction relay services
- `@safe-global/safe-core-sdk-types`: TypeScript types

### 2. Environment Configuration

Copy the environment template:
```bash
cp .env.example .env
```

Configure the following variables:
```env
# API Keys
REACT_APP_ALCHEMY_KEY=your_alchemy_key
REACT_APP_INFURA_KEY=your_infura_key

# RPC URLs
REACT_APP_ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY
REACT_APP_SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_KEY
REACT_APP_ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# Contract Addresses (update with deployed SafeTxPool addresses)
REACT_APP_SAFE_TX_POOL_MAINNET=0x...
REACT_APP_SAFE_TX_POOL_SEPOLIA=0x...
REACT_APP_SAFE_TX_POOL_ARBITRUM=0x...
```

### 3. Deploy SafeTxPool Contracts

Before using the interface, deploy the SafeTxPool contracts from vito-contracts:

```bash
cd vito-contracts
forge script script/DeploySafeTxPool.s.sol --rpc-url $RPC_URL --private-key $PRIVATE_KEY --broadcast
```

Update the contract addresses in your `.env` file.

## Usage Guide

### Wallet Connection

1. **Connect MetaMask**:
   ```
   :connect-metamask
   ```

2. **Check wallet status**:
   ```
   :wallet-status
   ```

3. **Switch networks**:
   ```
   :switch-network 1        # Ethereum mainnet
   :switch-network 11155111 # Sepolia testnet
   :switch-network 42161    # Arbitrum One
   ```

### Safe Wallet Management

Navigate to the Safe Wallet section:
```
:safe
```

The Safe section provides:
- Wallet connection status
- Safe information (owners, threshold, nonce)
- Pending transaction management

### Transaction Operations

1. **Propose a transaction**:
   ```
   :propose 0xSafeAddress 0xRecipientAddress 1.5
   ```

2. **Sign a pending transaction**:
   ```
   :sign 0xTransactionHash
   ```

3. **Execute a signed transaction**:
   ```
   :execute 0xTransactionHash
   ```

4. **Send transaction (propose + sign)**:
   ```
   :send 0xSafeAddress 0xRecipientAddress 1.5
   ```

### Navigation Commands

- `:home` - Home dashboard
- `:assets` - Asset management
- `:transactions` - Transaction history
- `:safe` - Safe Wallet management
- `:settings` - Settings

## Technical Details

### Transaction Lifecycle

1. **Proposal**: Transaction is proposed to SafeTxPool contract
2. **Signing**: Safe owners sign the transaction hash
3. **Execution**: When threshold is met, transaction can be executed
4. **Completion**: Transaction is executed on-chain and marked as complete

### Error Handling

The system includes comprehensive error handling:
- Wallet connection errors
- Network mismatch detection
- Transaction validation
- Gas estimation failures
- Signature verification

### Security Considerations

- All transactions require proper Safe multi-signature approval
- Private keys never leave the user's wallet
- Contract interactions are validated
- Network security through established RPC providers

## Development

### Adding New Networks

1. Update `NETWORK_CONFIGS` in `blockchainService.ts`
2. Add contract addresses for the new network
3. Update environment variables
4. Test wallet connection and transactions

### Extending Transaction Types

1. Update transaction interfaces in `SafeWallet.ts`
2. Modify UI components to handle new fields
3. Update command handlers for new operations
4. Add appropriate validation

### Testing

The integration can be tested on:
- Sepolia testnet (recommended for development)
- Local hardhat network
- Mainnet (production only)

## Troubleshooting

### Common Issues

1. **Wallet not connecting**:
   - Ensure MetaMask is installed
   - Check network configuration
   - Verify RPC URLs

2. **Transaction failures**:
   - Check Safe threshold requirements
   - Verify sufficient gas
   - Ensure correct contract addresses

3. **Network issues**:
   - Verify RPC endpoint availability
   - Check API key limits
   - Confirm network ID matches

### Support

For issues related to:
- Safe SDK: Check Safe documentation
- Contract deployment: See vito-contracts README
- UI/UX: Review component documentation

## Future Enhancements

Planned improvements:
- WalletConnect integration
- Hardware wallet support
- Batch transaction operations
- Advanced transaction filtering
- Real-time event monitoring
- Mobile responsive design

## Contributing

When contributing to Safe Wallet integration:
1. Follow existing code patterns
2. Add comprehensive error handling
3. Update documentation
4. Test on multiple networks
5. Consider security implications
