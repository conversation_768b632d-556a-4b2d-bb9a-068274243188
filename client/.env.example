# Environment variables template
# Copy this file to .env and add your actual keys

# API keys for Ethereum providers
REACT_APP_INFURA_KEY=YOUR_INFURA_KEY_HERE
REACT_APP_ALCHEMY_KEY=YOUR_ALCHEMY_KEY_HERE

# Blockchain RPC URLs
REACT_APP_ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
REACT_APP_SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
REACT_APP_ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# SafeTxPool Contract Addresses (deployed contract addresses)
REACT_APP_SAFE_TX_POOL_MAINNET=******************************************
REACT_APP_SAFE_TX_POOL_SEPOLIA=******************************************
REACT_APP_SAFE_TX_POOL_ARBITRUM=******************************************

# Safe API URLs (these are the official Safe API endpoints)
REACT_APP_SAFE_API_MAINNET=https://safe-transaction-mainnet.safe.global
REACT_APP_SAFE_API_SEPOLIA=https://safe-transaction-sepolia.safe.global
REACT_APP_SAFE_API_ARBITRUM=https://safe-transaction-arbitrum.safe.global