body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

html {
  overflow: hidden;
  height: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Prevent scrolling on the main app container */
#root {
  height: 100%;
  overflow: hidden;
}

/* Handle mobile browsers and prevent URL bar overlap */
@media screen and (max-width: 768px) {
  body {
    height: -webkit-fill-available;
  }
  
  html {
    height: -webkit-fill-available;
  }
  
  /* Move command bar up on mobile to avoid overlap with browser UI */
  .status-bar, .command-input {
    bottom: env(safe-area-inset-bottom, 10px) !important;
  }
}

/* Apply safe area insets (for iOS devices with notches) */
@supports (padding: max(0px)) {
  body {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}
