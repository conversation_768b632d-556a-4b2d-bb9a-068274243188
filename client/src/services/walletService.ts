import { ethers } from 'ethers';

export interface WalletConnection {
  signer: ethers.Signer;
  address: string;
  provider: ethers.providers.Provider;
}

export class WalletService {
  private static instance: WalletService;
  private connection: WalletConnection | null = null;

  private constructor() {}

  static getInstance(): WalletService {
    if (!WalletService.instance) {
      WalletService.instance = new WalletService();
    }
    return WalletService.instance;
  }

  /**
   * Connect to MetaMask or other injected wallet
   */
  async connectMetaMask(): Promise<WalletConnection> {
    if (typeof window.ethereum === 'undefined') {
      throw new Error('MetaMask is not installed');
    }

    try {
      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      // Create provider and signer
      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const signer = provider.getSigner();
      const address = await signer.getAddress();

      this.connection = {
        signer,
        address,
        provider
      };

      return this.connection;
    } catch (error) {
      console.error('Error connecting to MetaMask:', error);
      throw error;
    }
  }

  /**
   * Connect using WalletConnect
   */
  async connectWalletConnect(): Promise<WalletConnection> {
    // This would require WalletConnect integration
    // For now, we'll throw an error indicating it's not implemented
    throw new Error('WalletConnect integration not yet implemented');
  }

  /**
   * Connect using a private key (for testing purposes only)
   */
  async connectWithPrivateKey(privateKey: string, rpcUrl: string): Promise<WalletConnection> {
    try {
      const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
      const signer = new ethers.Wallet(privateKey, provider);
      const address = await signer.getAddress();

      this.connection = {
        signer,
        address,
        provider
      };

      return this.connection;
    } catch (error) {
      console.error('Error connecting with private key:', error);
      throw error;
    }
  }

  /**
   * Get current wallet connection
   */
  getConnection(): WalletConnection | null {
    return this.connection;
  }

  /**
   * Disconnect wallet
   */
  disconnect(): void {
    this.connection = null;
  }

  /**
   * Check if wallet is connected
   */
  isConnected(): boolean {
    return this.connection !== null;
  }

  /**
   * Get current signer
   */
  getSigner(): ethers.Signer | null {
    return this.connection?.signer || null;
  }

  /**
   * Get current address
   */
  getAddress(): string | null {
    return this.connection?.address || null;
  }

  /**
   * Switch network in MetaMask
   */
  async switchNetwork(chainId: number): Promise<void> {
    if (typeof window.ethereum === 'undefined') {
      throw new Error('MetaMask is not installed');
    }

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${chainId.toString(16)}` }],
      });
    } catch (error: any) {
      // This error code indicates that the chain has not been added to MetaMask
      if (error.code === 4902) {
        throw new Error('Network not added to MetaMask');
      }
      throw error;
    }
  }

  /**
   * Add network to MetaMask
   */
  async addNetwork(networkConfig: {
    chainId: number;
    chainName: string;
    rpcUrls: string[];
    nativeCurrency: {
      name: string;
      symbol: string;
      decimals: number;
    };
    blockExplorerUrls?: string[];
  }): Promise<void> {
    if (typeof window.ethereum === 'undefined') {
      throw new Error('MetaMask is not installed');
    }

    try {
      await window.ethereum.request({
        method: 'wallet_addEthereumChain',
        params: [{
          chainId: `0x${networkConfig.chainId.toString(16)}`,
          chainName: networkConfig.chainName,
          rpcUrls: networkConfig.rpcUrls,
          nativeCurrency: networkConfig.nativeCurrency,
          blockExplorerUrls: networkConfig.blockExplorerUrls,
        }],
      });
    } catch (error) {
      console.error('Error adding network to MetaMask:', error);
      throw error;
    }
  }

  /**
   * Sign a message
   */
  async signMessage(message: string): Promise<string> {
    if (!this.connection) {
      throw new Error('Wallet not connected');
    }

    try {
      return await this.connection.signer.signMessage(message);
    } catch (error) {
      console.error('Error signing message:', error);
      throw error;
    }
  }

  /**
   * Get network information
   */
  async getNetwork(): Promise<ethers.providers.Network> {
    if (!this.connection) {
      throw new Error('Wallet not connected');
    }

    return await this.connection.provider.getNetwork();
  }

  /**
   * Get balance for current address
   */
  async getBalance(): Promise<string> {
    if (!this.connection) {
      throw new Error('Wallet not connected');
    }

    const balance = await this.connection.provider.getBalance(this.connection.address);
    return ethers.utils.formatEther(balance);
  }
}

// Global wallet service instance
export const walletService = WalletService.getInstance();

// Extend Window interface for TypeScript
declare global {
  interface Window {
    ethereum?: any;
  }
}
