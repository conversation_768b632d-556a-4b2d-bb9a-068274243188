import { ethers } from 'ethers';
import Safe, { EthersAdapter } from '@safe-global/protocol-kit';
import SafeApiKit from '@safe-global/api-kit';
import { SafeTransactionDataPartial } from '@safe-global/safe-core-sdk-types';

// Network configuration
export interface NetworkConfig {
  chainId: number;
  rpcUrl: string;
  safeServiceUrl: string;
  name: string;
}

export const NETWORK_CONFIGS: Record<string, NetworkConfig> = {
  ethereum: {
    chainId: 1,
    rpcUrl: process.env.REACT_APP_ETHEREUM_RPC_URL || 'https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
    safeServiceUrl: 'https://safe-transaction-mainnet.safe.global',
    name: 'Ethereum Mainnet'
  },
  sepolia: {
    chainId: 11155111,
    rpcUrl: process.env.REACT_APP_SEPOLIA_RPC_URL || 'https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
    safeServiceUrl: 'https://safe-transaction-sepolia.safe.global',
    name: '<PERSON>olia Testnet'
  },
  arbitrum: {
    chainId: 42161,
    rpcUrl: process.env.REACT_APP_ARBITRUM_RPC_URL || 'https://arb1.arbitrum.io/rpc',
    safeServiceUrl: 'https://safe-transaction-arbitrum.safe.global',
    name: 'Arbitrum One'
  }
};

// SafeTxPool contract ABI (from vito-contracts)
export const SAFE_TX_POOL_ABI = [
  "function proposeTx(bytes32 txHash, address safe, address to, uint256 value, bytes calldata data, uint8 operation, uint256 nonce) external",
  "function signTx(bytes32 txHash, bytes calldata signature) external",
  "function markAsExecuted(bytes32 txHash) external",
  "function getTxDetails(bytes32 txHash) external view returns (address safe, address to, uint256 value, bytes memory data, uint8 operation, address proposer, uint256 nonce, uint256 txId)",
  "function getSignatures(bytes32 txHash) external view returns (bytes[] memory)",
  "function hasSignedTx(bytes32 txHash, address signer) external view returns (bool)",
  "function getPendingTxHashes(address safe, uint256 offset, uint256 limit) external view returns (bytes32[] memory)",
  "function deleteTx(bytes32 txHash) external",
  "event TransactionProposed(bytes32 indexed txHash, address indexed proposer, address indexed safe, address to, uint256 value, bytes data, uint8 operation, uint256 nonce, uint256 txId)",
  "event TransactionSigned(bytes32 indexed txHash, address indexed signer, bytes signature, uint256 txId)",
  "event TransactionExecuted(bytes32 indexed txHash, address indexed safe, uint256 txId)"
];

// Contract addresses for SafeTxPool (these would be deployed addresses)
export const SAFE_TX_POOL_ADDRESSES: Record<string, string> = {
  ethereum: process.env.REACT_APP_SAFE_TX_POOL_MAINNET || '******************************************',
  sepolia: process.env.REACT_APP_SAFE_TX_POOL_SEPOLIA || '******************************************',
  arbitrum: process.env.REACT_APP_SAFE_TX_POOL_ARBITRUM || '******************************************'
};

export class BlockchainService {
  private provider: ethers.providers.Provider;
  private signer?: ethers.Signer;
  private safeApiKit: SafeApiKit;
  private networkConfig: NetworkConfig;
  private safeTxPoolContract: ethers.Contract;

  constructor(network: string = 'ethereum') {
    this.networkConfig = NETWORK_CONFIGS[network];
    if (!this.networkConfig) {
      throw new Error(`Unsupported network: ${network}`);
    }

    // Initialize provider
    this.provider = new ethers.providers.JsonRpcProvider(this.networkConfig.rpcUrl);
    
    // Initialize Safe API Kit
    this.safeApiKit = new SafeApiKit({
      txServiceUrl: this.networkConfig.safeServiceUrl,
      ethAdapter: new EthersAdapter({
        ethers,
        signerOrProvider: this.provider
      })
    });

    // Initialize SafeTxPool contract
    const contractAddress = SAFE_TX_POOL_ADDRESSES[network];
    this.safeTxPoolContract = new ethers.Contract(
      contractAddress,
      SAFE_TX_POOL_ABI,
      this.provider
    );
  }

  /**
   * Connect a signer (wallet) to the service
   */
  async connectSigner(signer: ethers.Signer): Promise<void> {
    this.signer = signer;
    
    // Update SafeTxPool contract with signer
    this.safeTxPoolContract = this.safeTxPoolContract.connect(signer);
    
    // Update Safe API Kit with signer
    this.safeApiKit = new SafeApiKit({
      txServiceUrl: this.networkConfig.safeServiceUrl,
      ethAdapter: new EthersAdapter({
        ethers,
        signerOrProvider: signer
      })
    });
  }

  /**
   * Get Safe instance for a given address
   */
  async getSafeInstance(safeAddress: string): Promise<Safe> {
    if (!this.signer) {
      throw new Error('Signer not connected');
    }

    const ethAdapter = new EthersAdapter({
      ethers,
      signerOrProvider: this.signer
    });

    return await Safe.create({
      ethAdapter,
      safeAddress
    });
  }

  /**
   * Create a Safe transaction
   */
  async createSafeTransaction(
    safeAddress: string,
    transactions: SafeTransactionDataPartial[]
  ): Promise<any> {
    const safeInstance = await this.getSafeInstance(safeAddress);
    
    const safeTransaction = await safeInstance.createTransaction({
      safeTransactionData: transactions
    });

    return safeTransaction;
  }

  /**
   * Propose a transaction to the SafeTxPool
   */
  async proposeTransaction(
    safeAddress: string,
    transaction: SafeTransactionDataPartial
  ): Promise<string> {
    if (!this.signer) {
      throw new Error('Signer not connected');
    }

    const safeInstance = await this.getSafeInstance(safeAddress);
    const safeTransaction = await safeInstance.createTransaction({
      safeTransactionData: transaction
    });

    // Get transaction hash
    const txHash = await safeInstance.getTransactionHash(safeTransaction);
    
    // Get nonce
    const nonce = await safeInstance.getNonce();

    // Propose to SafeTxPool
    const tx = await this.safeTxPoolContract.proposeTx(
      txHash,
      safeAddress,
      transaction.to,
      transaction.value || 0,
      transaction.data || '0x',
      transaction.operation || 0,
      nonce
    );

    await tx.wait();
    return txHash;
  }

  /**
   * Sign a transaction in the SafeTxPool
   */
  async signTransaction(txHash: string): Promise<void> {
    if (!this.signer) {
      throw new Error('Signer not connected');
    }

    // Get transaction details from SafeTxPool
    const txDetails = await this.safeTxPoolContract.getTxDetails(txHash);
    
    // Create Safe instance
    const safeInstance = await this.getSafeInstance(txDetails.safe);
    
    // Recreate the transaction
    const safeTransaction = await safeInstance.createTransaction({
      safeTransactionData: {
        to: txDetails.to,
        value: txDetails.value.toString(),
        data: txDetails.data,
        operation: txDetails.operation
      }
    });

    // Sign the transaction
    const signature = await safeInstance.signTransactionHash(txHash);
    
    // Submit signature to SafeTxPool
    const tx = await this.safeTxPoolContract.signTx(txHash, signature.data);
    await tx.wait();
  }

  /**
   * Execute a Safe transaction
   */
  async executeTransaction(txHash: string): Promise<string> {
    if (!this.signer) {
      throw new Error('Signer not connected');
    }

    // Get transaction details from SafeTxPool
    const txDetails = await this.safeTxPoolContract.getTxDetails(txHash);
    
    // Create Safe instance
    const safeInstance = await this.getSafeInstance(txDetails.safe);
    
    // Get all signatures from SafeTxPool
    const signatures = await this.safeTxPoolContract.getSignatures(txHash);
    
    // Recreate the transaction with signatures
    const safeTransaction = await safeInstance.createTransaction({
      safeTransactionData: {
        to: txDetails.to,
        value: txDetails.value.toString(),
        data: txDetails.data,
        operation: txDetails.operation
      }
    });

    // Add signatures to transaction
    signatures.forEach((sig: string) => {
      safeTransaction.addSignature(sig);
    });

    // Execute the transaction
    const executeTxResponse = await safeInstance.executeTransaction(safeTransaction);
    await executeTxResponse.transactionResponse?.wait();

    return executeTxResponse.hash;
  }

  /**
   * Get pending transactions for a Safe
   */
  async getPendingTransactions(safeAddress: string, offset: number = 0, limit: number = 10): Promise<string[]> {
    const txHashes = await this.safeTxPoolContract.getPendingTxHashes(safeAddress, offset, limit);
    return txHashes;
  }

  /**
   * Get transaction details
   */
  async getTransactionDetails(txHash: string): Promise<any> {
    return await this.safeTxPoolContract.getTxDetails(txHash);
  }

  /**
   * Check if an address has signed a transaction
   */
  async hasSignedTransaction(txHash: string, signerAddress: string): Promise<boolean> {
    return await this.safeTxPoolContract.hasSignedTx(txHash, signerAddress);
  }

  /**
   * Delete a pending transaction
   */
  async deleteTransaction(txHash: string): Promise<void> {
    if (!this.signer) {
      throw new Error('Signer not connected');
    }

    const tx = await this.safeTxPoolContract.deleteTx(txHash);
    await tx.wait();
  }

  /**
   * Get Safe information
   */
  async getSafeInfo(safeAddress: string): Promise<any> {
    return await this.safeApiKit.getSafeInfo(safeAddress);
  }

  /**
   * Get Safe transaction history
   */
  async getTransactionHistory(safeAddress: string): Promise<any> {
    return await this.safeApiKit.getMultisigTransactions(safeAddress);
  }
}
