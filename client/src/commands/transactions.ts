// Transaction command handlers
import { sendTransaction as sendSafeTransaction, signTransaction, executeTransaction } from '../models/SafeWallet';
import { walletService } from '../services/walletService';

// Real implementation for sending transactions via Safe Wallet
const sendTransaction = async (from: string, to: string, amount: string, network: string = 'ethereum') => {
  const signer = walletService.getSigner();
  if (!signer) {
    throw new Error('Wallet not connected. Please connect your wallet first.');
  }

  return await sendSafeTransaction(from, to, amount, network, signer);
};

const handleTransactionCommands = (
  command: string,
  callbacks: {
    updateTransactions: (tx: any) => void,
    network?: string
  }
) => {
  const commandParts = command.split(' ');
  const action = commandParts[0].toLowerCase();
  const network = callbacks.network || 'ethereum';

  switch (action) {
    case 'send':
      if (commandParts.length >= 4) {
        const [_, from, to, amount] = commandParts;
        handleSendTransaction(from, to, amount, network, callbacks.updateTransactions);
        return true;
      } else {
        alert('Usage: send [from_address] [to_address] [amount]');
        return true;
      }
    case 'sign':
      if (commandParts.length >= 2) {
        const [_, txHash] = commandParts;
        handleSignTransaction(txHash, network);
        return true;
      } else {
        alert('Usage: sign [transaction_hash]');
        return true;
      }
    case 'execute':
      if (commandParts.length >= 2) {
        const [_, txHash] = commandParts;
        handleExecuteTransaction(txHash, network);
        return true;
      } else {
        alert('Usage: execute [transaction_hash]');
        return true;
      }
    case 'propose':
      if (commandParts.length >= 4) {
        const [_, from, to, amount] = commandParts;
        handleProposeTransaction(from, to, amount, network, callbacks.updateTransactions);
        return true;
      } else {
        alert('Usage: propose [from_address] [to_address] [amount]');
        return true;
      }
    default:
      return false;
  }
};

// Helper function to handle send transaction logic
const handleSendTransaction = async (
  from: string,
  to: string,
  amount: string,
  network: string,
  updateTransactions: (tx: any) => void
) => {
  try {
    const tx = await sendTransaction(from, to, amount, network);
    updateTransactions(tx);
    alert(`Transaction proposed: ${amount} ETH from ${from} to ${to}. Transaction Hash: ${tx.txHash}`);
  } catch (error: any) {
    console.error('Error sending transaction:', error);
    alert(`Failed to send transaction: ${error.message}`);
  }
};

// Helper function to handle propose transaction logic (same as send but different messaging)
const handleProposeTransaction = async (
  from: string,
  to: string,
  amount: string,
  network: string,
  updateTransactions: (tx: any) => void
) => {
  try {
    const tx = await sendTransaction(from, to, amount, network);
    updateTransactions(tx);
    alert(`Transaction proposed to SafeTxPool: ${amount} ETH from ${from} to ${to}. Awaiting signatures. Hash: ${tx.txHash}`);
  } catch (error: any) {
    console.error('Error proposing transaction:', error);
    alert(`Failed to propose transaction: ${error.message}`);
  }
};

// Helper function to handle sign transaction logic
const handleSignTransaction = async (
  txHash: string,
  network: string
) => {
  try {
    const signer = walletService.getSigner();
    if (!signer) {
      throw new Error('Wallet not connected. Please connect your wallet first.');
    }

    await signTransaction(txHash, network, signer);
    alert(`Transaction signed successfully: ${txHash}`);
  } catch (error: any) {
    console.error('Error signing transaction:', error);
    alert(`Failed to sign transaction: ${error.message}`);
  }
};

// Helper function to handle execute transaction logic
const handleExecuteTransaction = async (
  txHash: string,
  network: string
) => {
  try {
    const signer = walletService.getSigner();
    if (!signer) {
      throw new Error('Wallet not connected. Please connect your wallet first.');
    }

    const executedTxHash = await executeTransaction(txHash, network, signer);
    alert(`Transaction executed successfully! Execution Hash: ${executedTxHash}`);
  } catch (error: any) {
    console.error('Error executing transaction:', error);
    alert(`Failed to execute transaction: ${error.message}`);
  }
};

export default handleTransactionCommands;