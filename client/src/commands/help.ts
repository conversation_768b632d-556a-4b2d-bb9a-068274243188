// Help command handlers

const handleHelpCommand = (command: string) => {
  const cmd = command.trim().toLowerCase();

  if (cmd === 'help') {
    const helpMessage =
      'Available commands:\n\n' +
      'WALLET COMMANDS:\n' +
      '- :q - Disconnect wallet\n' +
      '- :c - Connect wallet\n' +
      '- :connect-metamask - Connect MetaMask wallet\n' +
      '- :wallet-status - Show wallet connection status\n' +
      '- :switch-network [chain_id] - Switch to different network\n\n' +
      'NAVIGATION COMMANDS:\n' +
      '- :home - Switch to home section\n' +
      '- :assets (ast) - Switch to assets section\n' +
      '- :transactions (txs) - Switch to transactions section\n' +
      '- :safe - Switch to Safe Wallet management\n' +
      '- :settings (set) - Switch to settings section\n\n' +
      'SAFE TRANSACTION COMMANDS:\n' +
      '- :send [from] [to] [amount] - Send a transaction\n' +
      '- :propose [from] [to] [amount] - Propose transaction to SafeTxPool\n' +
      '- :sign [tx_hash] - Sign a pending transaction\n' +
      '- :execute [tx_hash] - Execute a signed transaction\n\n' +
      'HELP:\n' +
      '- :help - Show this help message\n\n' +
      'Note: All amounts are in ETH. Transaction hashes can be found in the Safe Wallet section.';

    alert(helpMessage);
    return true;
  }

  return false;
};

export default handleHelpCommand;