// Wallet command handlers
import { walletService } from '../services/walletService';

const handleWalletCommands = (
  command: string,
  callbacks: {
    disconnect: () => void,
    connect: () => void,
    onWalletConnected?: (address: string) => void
  }
) => {
  const commandParts = command.trim().toLowerCase().split(' ');
  const cmd = commandParts[0];

  switch (cmd) {
    case 'q':
      handleDisconnectWallet(callbacks.disconnect);
      return true;
    case 'c':
      callbacks.connect();
      return true;
    case 'connect-metamask':
      handleConnectMetaMask(callbacks.onWalletConnected);
      return true;
    case 'connect-wallet':
      handleConnectMetaMask(callbacks.onWalletConnected);
      return true;
    case 'wallet-status':
      handleWalletStatus();
      return true;
    case 'switch-network':
      if (commandParts.length >= 2) {
        const chainId = parseInt(commandParts[1]);
        handleSwitchNetwork(chainId);
        return true;
      } else {
        alert('Usage: switch-network [chain_id] (e.g., switch-network 1 for Ethereum mainnet)');
        return true;
      }
    default:
      return false;
  }
};

// Helper functions for wallet operations
const handleConnectMetaMask = async (onWalletConnected?: (address: string) => void) => {
  try {
    const connection = await walletService.connectMetaMask();
    alert(`MetaMask connected successfully! Address: ${connection.address}`);

    if (onWalletConnected) {
      onWalletConnected(connection.address);
    }
  } catch (error: any) {
    console.error('Error connecting MetaMask:', error);
    alert(`Failed to connect MetaMask: ${error.message}`);
  }
};

const handleDisconnectWallet = (disconnect: () => void) => {
  walletService.disconnect();
  disconnect();
  alert('Wallet disconnected successfully');
};

const handleWalletStatus = async () => {
  try {
    if (!walletService.isConnected()) {
      alert('No wallet connected');
      return;
    }

    const address = walletService.getAddress();
    const network = await walletService.getNetwork();
    const balance = await walletService.getBalance();

    alert(`Wallet Status:
Address: ${address}
Network: ${network.name} (Chain ID: ${network.chainId})
Balance: ${balance} ETH`);
  } catch (error: any) {
    console.error('Error getting wallet status:', error);
    alert(`Failed to get wallet status: ${error.message}`);
  }
};

const handleSwitchNetwork = async (chainId: number) => {
  try {
    await walletService.switchNetwork(chainId);
    alert(`Successfully switched to network with Chain ID: ${chainId}`);
  } catch (error: any) {
    console.error('Error switching network:', error);
    alert(`Failed to switch network: ${error.message}`);
  }
};

// Helper for global wallet commands
export const dispatchGlobalWalletCommand = (command: string) => {
  window.dispatchEvent(new CustomEvent('vito:command', {
    detail: { command }
  }));
};

export default handleWalletCommands;