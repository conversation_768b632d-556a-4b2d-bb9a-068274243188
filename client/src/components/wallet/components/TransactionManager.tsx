import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Transaction } from '../../../models/SafeWallet';
import { getPendingTransactions, signTransaction, executeTransaction } from '../../../models/SafeWallet';
import { walletService } from '../../../services/walletService';

interface TransactionManagerProps {
  safeAddress: string;
  network: string;
  onTransactionUpdate?: () => void;
}

const TransactionManagerContainer = styled.div`
  padding: 1rem;
  background: #1a1a1a;
  border-radius: 8px;
  margin: 1rem 0;
`;

const TransactionItem = styled.div`
  background: #2a2a2a;
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
  border-left: 4px solid #4CAF50;
`;

const TransactionHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const TransactionHash = styled.code`
  background: #333;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #4CAF50;
  word-break: break-all;
`;

const TransactionDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
`;

const StatusBadge = styled.span<{ status: string }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  background: ${props => {
    switch (props.status) {
      case 'awaiting_signatures': return '#ff9800';
      case 'ready_to_execute': return '#4CAF50';
      case 'completed': return '#2196F3';
      case 'failed': return '#f44336';
      default: return '#666';
    }
  }};
  color: white;
`;

const ActionButton = styled.button`
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin: 0.25rem;
  font-size: 0.8rem;
  
  &:hover {
    background: #45a049;
  }
  
  &:disabled {
    background: #666;
    cursor: not-allowed;
  }
`;

const LoadingSpinner = styled.div`
  border: 2px solid #333;
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const TransactionManager: React.FC<TransactionManagerProps> = ({
  safeAddress,
  network,
  onTransactionUpdate
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [signingTx, setSigningTx] = useState<string | null>(null);
  const [executingTx, setExecutingTx] = useState<string | null>(null);

  // Load pending transactions
  const loadTransactions = async () => {
    setLoading(true);
    try {
      const pendingTxs = await getPendingTransactions(safeAddress, network);
      setTransactions(pendingTxs);
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Sign a transaction
  const handleSignTransaction = async (txHash: string) => {
    if (!walletService.isConnected()) {
      alert('Please connect your wallet first');
      return;
    }

    setSigningTx(txHash);
    try {
      const signer = walletService.getSigner();
      if (!signer) {
        throw new Error('No signer available');
      }

      await signTransaction(txHash, network, signer);
      alert('Transaction signed successfully!');
      
      // Reload transactions
      await loadTransactions();
      
      if (onTransactionUpdate) {
        onTransactionUpdate();
      }
    } catch (error: any) {
      console.error('Error signing transaction:', error);
      alert(`Failed to sign transaction: ${error.message}`);
    } finally {
      setSigningTx(null);
    }
  };

  // Execute a transaction
  const handleExecuteTransaction = async (txHash: string) => {
    if (!walletService.isConnected()) {
      alert('Please connect your wallet first');
      return;
    }

    setExecutingTx(txHash);
    try {
      const signer = walletService.getSigner();
      if (!signer) {
        throw new Error('No signer available');
      }

      const executedTxHash = await executeTransaction(txHash, network, signer);
      alert(`Transaction executed successfully! Hash: ${executedTxHash}`);
      
      // Reload transactions
      await loadTransactions();
      
      if (onTransactionUpdate) {
        onTransactionUpdate();
      }
    } catch (error: any) {
      console.error('Error executing transaction:', error);
      alert(`Failed to execute transaction: ${error.message}`);
    } finally {
      setExecutingTx(null);
    }
  };

  // Load transactions on component mount and when dependencies change
  useEffect(() => {
    if (safeAddress) {
      loadTransactions();
    }
  }, [safeAddress, network]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatAmount = (amount: string) => {
    return `${parseFloat(amount).toFixed(4)} ETH`;
  };

  if (loading) {
    return (
      <TransactionManagerContainer>
        <h3>Pending Transactions</h3>
        <LoadingSpinner />
      </TransactionManagerContainer>
    );
  }

  return (
    <TransactionManagerContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h3>Pending Transactions ({transactions.length})</h3>
        <ActionButton onClick={loadTransactions}>
          Refresh
        </ActionButton>
      </div>
      
      {transactions.length === 0 ? (
        <p style={{ color: '#666', textAlign: 'center', padding: '2rem' }}>
          No pending transactions
        </p>
      ) : (
        transactions.map((tx) => (
          <TransactionItem key={tx.id}>
            <TransactionHeader>
              <StatusBadge status={tx.status}>{tx.status.replace('_', ' ').toUpperCase()}</StatusBadge>
              <div>
                {tx.txHash && (
                  <TransactionHash>
                    {formatAddress(tx.txHash)}
                  </TransactionHash>
                )}
              </div>
            </TransactionHeader>
            
            <TransactionDetails>
              <div>
                <strong>To:</strong> {formatAddress(tx.to)}
              </div>
              <div>
                <strong>Amount:</strong> {formatAmount(tx.amount)}
              </div>
              <div>
                <strong>From:</strong> {formatAddress(tx.from)}
              </div>
              <div>
                <strong>Nonce:</strong> {tx.nonce || 'N/A'}
              </div>
            </TransactionDetails>
            
            <div style={{ marginTop: '1rem' }}>
              <ActionButton
                onClick={() => handleSignTransaction(tx.txHash!)}
                disabled={signingTx === tx.txHash}
              >
                {signingTx === tx.txHash ? 'Signing...' : 'Sign Transaction'}
              </ActionButton>
              
              <ActionButton
                onClick={() => handleExecuteTransaction(tx.txHash!)}
                disabled={executingTx === tx.txHash || tx.status !== 'ready_to_execute'}
              >
                {executingTx === tx.txHash ? 'Executing...' : 'Execute Transaction'}
              </ActionButton>
            </div>
          </TransactionItem>
        ))
      )}
    </TransactionManagerContainer>
  );
};

export default TransactionManager;
