import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { walletService } from '../../../services/walletService';
import { getSafeInfo } from '../../../models/SafeWallet';

interface SafeConnectionProps {
  safeAddress: string;
  network: string;
  onConnectionChange?: (connected: boolean) => void;
}

const ConnectionContainer = styled.div`
  padding: 1rem;
  background: #1a1a1a;
  border-radius: 8px;
  margin: 1rem 0;
`;

const ConnectionStatus = styled.div<{ connected: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  
  &::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: ${props => props.connected ? '#4CAF50' : '#f44336'};
  }
`;

const SafeInfo = styled.div`
  background: #2a2a2a;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1rem 0;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const Label = styled.span`
  font-size: 0.8rem;
  color: #888;
  text-transform: uppercase;
`;

const Value = styled.span`
  font-weight: bold;
  color: #fff;
`;

const ConnectButton = styled.button`
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  width: 100%;
  margin: 0.5rem 0;
  
  &:hover {
    background: #45a049;
  }
  
  &:disabled {
    background: #666;
    cursor: not-allowed;
  }
`;

const DisconnectButton = styled(ConnectButton)`
  background: #f44336;
  
  &:hover {
    background: #da190b;
  }
`;

const OwnersList = styled.div`
  margin: 1rem 0;
`;

const OwnerItem = styled.div`
  background: #333;
  padding: 0.5rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9rem;
`;

const LoadingSpinner = styled.div`
  border: 2px solid #333;
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SafeConnection: React.FC<SafeConnectionProps> = ({
  safeAddress,
  network,
  onConnectionChange
}) => {
  const [walletConnected, setWalletConnected] = useState(false);
  const [safeInfo, setSafeInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [connecting, setConnecting] = useState(false);

  // Check wallet connection status
  const checkWalletConnection = () => {
    const connected = walletService.isConnected();
    setWalletConnected(connected);
    
    if (onConnectionChange) {
      onConnectionChange(connected);
    }
  };

  // Load Safe information
  const loadSafeInfo = async () => {
    if (!safeAddress) return;
    
    setLoading(true);
    try {
      const info = await getSafeInfo(safeAddress, network);
      setSafeInfo(info);
    } catch (error) {
      console.error('Error loading Safe info:', error);
      setSafeInfo(null);
    } finally {
      setLoading(false);
    }
  };

  // Connect wallet
  const handleConnectWallet = async () => {
    setConnecting(true);
    try {
      await walletService.connectMetaMask();
      checkWalletConnection();
      await loadSafeInfo();
    } catch (error: any) {
      console.error('Error connecting wallet:', error);
      alert(`Failed to connect wallet: ${error.message}`);
    } finally {
      setConnecting(false);
    }
  };

  // Disconnect wallet
  const handleDisconnectWallet = () => {
    walletService.disconnect();
    checkWalletConnection();
    setSafeInfo(null);
  };

  // Check connection and load Safe info on mount
  useEffect(() => {
    checkWalletConnection();
    if (walletService.isConnected()) {
      loadSafeInfo();
    }
  }, [safeAddress, network]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <ConnectionContainer>
      <h3>Safe Wallet Connection</h3>
      
      <ConnectionStatus connected={walletConnected}>
        <span>
          {walletConnected ? 'Wallet Connected' : 'Wallet Disconnected'}
        </span>
      </ConnectionStatus>

      {!walletConnected ? (
        <ConnectButton 
          onClick={handleConnectWallet}
          disabled={connecting}
        >
          {connecting ? 'Connecting...' : 'Connect MetaMask'}
        </ConnectButton>
      ) : (
        <>
          <DisconnectButton onClick={handleDisconnectWallet}>
            Disconnect Wallet
          </DisconnectButton>
          
          <SafeInfo>
            <h4>Safe Information</h4>
            
            {loading ? (
              <LoadingSpinner />
            ) : safeInfo ? (
              <>
                <InfoGrid>
                  <InfoItem>
                    <Label>Safe Address</Label>
                    <Value>{formatAddress(safeAddress)}</Value>
                  </InfoItem>
                  <InfoItem>
                    <Label>Network</Label>
                    <Value>{network}</Value>
                  </InfoItem>
                  <InfoItem>
                    <Label>Threshold</Label>
                    <Value>{safeInfo.threshold || 'N/A'}</Value>
                  </InfoItem>
                  <InfoItem>
                    <Label>Nonce</Label>
                    <Value>{safeInfo.nonce || 'N/A'}</Value>
                  </InfoItem>
                </InfoGrid>
                
                {safeInfo.owners && safeInfo.owners.length > 0 && (
                  <OwnersList>
                    <Label>Owners ({safeInfo.owners.length})</Label>
                    {safeInfo.owners.map((owner: string, index: number) => (
                      <OwnerItem key={index}>
                        {owner}
                      </OwnerItem>
                    ))}
                  </OwnersList>
                )}
              </>
            ) : (
              <p style={{ color: '#888', textAlign: 'center' }}>
                Unable to load Safe information
              </p>
            )}
          </SafeInfo>
        </>
      )}
    </ConnectionContainer>
  );
};

export default SafeConnection;
