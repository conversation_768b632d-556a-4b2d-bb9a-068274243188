export interface WalletAccount {
  address: string;
  balance: string;
  name: string;
}

export interface Transaction {
  id: string;
  from: string;
  to: string;
  amount: string;
  status: 'pending' | 'completed' | 'failed' | 'awaiting_signatures' | 'ready_to_execute';
  timestamp: number;
  txHash?: string;
  nonce?: number;
  signatures?: string[];
  requiredSignatures?: number;
  data?: string;
  operation?: number;
}

export interface SafeWallet {
  accounts: WalletAccount[];
  transactions: Transaction[];
  owners?: string[];
  threshold?: number;
  nonce?: number;
}

// This is a mock implementation - in a real app, you would connect to actual Safe wallet API
export const createMockSafeWallet = (): SafeWallet => {
  return {
    accounts: [
      {
        address: '******************************************',
        balance: '10.5',
        name: 'Main Safe',
      },
      {
        address: '******************************************',
        balance: '5.2',
        name: 'Team Safe',
      },
    ],
    transactions: [
      {
        id: 'tx1',
        from: '******************************************',
        to: '******************************************',
        amount: '1.5',
        status: 'completed',
        timestamp: Date.now() - ********, // 1 day ago
      },
      {
        id: 'tx2',
        from: '******************************************',
        to: '******************************************',
        amount: '0.5',
        status: 'pending',
        timestamp: Date.now() - 3600000, // 1 hour ago
      },
    ],
  };
};

// Import blockchain service
import { BlockchainService } from '../services/blockchainService';
import { ethers } from 'ethers';

// Helper functions for safe wallet operations
export const sendTransaction = async (
  from: string,
  to: string,
  amount: string,
  network: string = 'ethereum',
  signer?: ethers.Signer
): Promise<Transaction> => {
  try {
    if (!signer) {
      throw new Error('Signer required for transaction');
    }

    const blockchainService = new BlockchainService(network);
    await blockchainService.connectSigner(signer);

    // Create transaction data
    const transactionData = {
      to,
      value: ethers.utils.parseEther(amount).toString(),
      data: '0x'
    };

    // Propose transaction to SafeTxPool
    const txHash = await blockchainService.proposeTransaction(from, transactionData);

    return {
      id: txHash,
      from,
      to,
      amount,
      status: 'awaiting_signatures',
      timestamp: Date.now(),
      txHash
    };
  } catch (error) {
    console.error('Error sending transaction:', error);
    throw error;
  }
};

export const getWalletBalance = async (
  address: string,
  network: string = 'ethereum'
): Promise<string> => {
  try {
    const blockchainService = new BlockchainService(network);
    const safeInfo = await blockchainService.getSafeInfo(address);

    // Convert balance from wei to ether
    const balanceInEther = ethers.utils.formatEther(safeInfo.balance || '0');
    return balanceInEther;
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    return '0';
  }
};

export const getSafeInfo = async (
  address: string,
  network: string = 'ethereum'
): Promise<Partial<SafeWallet>> => {
  try {
    const blockchainService = new BlockchainService(network);
    const safeInfo = await blockchainService.getSafeInfo(address);

    return {
      owners: safeInfo.owners,
      threshold: safeInfo.threshold,
      nonce: safeInfo.nonce
    };
  } catch (error) {
    console.error('Error getting Safe info:', error);
    return {};
  }
};

export const signTransaction = async (
  txHash: string,
  network: string = 'ethereum',
  signer?: ethers.Signer
): Promise<void> => {
  try {
    if (!signer) {
      throw new Error('Signer required for signing transaction');
    }

    const blockchainService = new BlockchainService(network);
    await blockchainService.connectSigner(signer);

    await blockchainService.signTransaction(txHash);
  } catch (error) {
    console.error('Error signing transaction:', error);
    throw error;
  }
};

export const executeTransaction = async (
  txHash: string,
  network: string = 'ethereum',
  signer?: ethers.Signer
): Promise<string> => {
  try {
    if (!signer) {
      throw new Error('Signer required for executing transaction');
    }

    const blockchainService = new BlockchainService(network);
    await blockchainService.connectSigner(signer);

    return await blockchainService.executeTransaction(txHash);
  } catch (error) {
    console.error('Error executing transaction:', error);
    throw error;
  }
};

export const getPendingTransactions = async (
  safeAddress: string,
  network: string = 'ethereum'
): Promise<Transaction[]> => {
  try {
    const blockchainService = new BlockchainService(network);
    const txHashes = await blockchainService.getPendingTransactions(safeAddress);

    const transactions: Transaction[] = [];

    for (const txHash of txHashes) {
      const txDetails = await blockchainService.getTransactionDetails(txHash);

      transactions.push({
        id: txHash,
        from: txDetails.safe,
        to: txDetails.to,
        amount: ethers.utils.formatEther(txDetails.value),
        status: 'awaiting_signatures',
        timestamp: Date.now(), // This would come from event logs in a real implementation
        txHash,
        nonce: txDetails.nonce.toNumber(),
        data: txDetails.data
      });
    }

    return transactions;
  } catch (error) {
    console.error('Error getting pending transactions:', error);
    return [];
  }
};